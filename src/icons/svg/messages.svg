<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>消息</title>
    <g id="后台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-1153.000000, -22.000000)" fill="#FFFFFF">
            <g id="消息" transform="translate(1153.000000, 22.000000)">
                <rect id="矩形" fill-rule="nonzero" opacity="0" x="0" y="0" width="20" height="20"></rect>
                <path d="M19.375,1.25 C19.7177519,1.25 19.9965725,1.52601529 20,1.86875 L20,15.61875 C20,15.9625 19.71875,16.24375 19.375,16.24375 L12.1375,16.24375 L9.19375,19.1875 C9.06875,19.30625 8.9125,19.368924 8.75,19.368924 C8.49764739,19.370133 8.26942793,19.2190254 8.17217328,18.9861622 C8.07491864,18.753299 7.81393875,16.8063629 7.7125,16.25 L0.625,16.25 C0.28125,16.25 0,15.96875 0,15.625 L0,1.875 C0,1.53125 0.28125,1.25 0.625,1.25 Z M4.94375,7.8625 C4.35625,7.86875 3.88125,8.33125 3.8625,8.91875 C3.84906558,9.31381096 4.05216197,9.68474998 4.39223114,9.8862608 C4.7323003,10.0877716 5.1551997,10.0877716 5.49526886,9.8862608 C5.83533803,9.68474998 6.03843442,9.31381096 6.025,8.91875 C6.0125,8.33125 5.53125,7.8625 4.94375,7.8625 Z M9.44223114,7.9512392 C9.10216197,8.15275002 8.89906558,8.52368904 8.9125,8.91875 C8.925,9.50625 9.40625,9.975 9.99375,9.975 C10.58125,9.975 11.0625,9.50625 11.075,8.91875 C11.0884344,8.52368904 10.885338,8.15275002 10.5452689,7.9512392 C10.2051997,7.74972838 9.7823003,7.74972838 9.44223114,7.9512392 Z M14.4922311,7.9512392 C14.152162,8.15275002 13.9490656,8.52368904 13.9625,8.91875 C13.975,9.50625 14.45625,9.975 15.04375,9.975 C15.625,9.975 16.10625,9.50625 16.125,8.91875 C16.1384344,8.52368904 15.935338,8.15275002 15.5952689,7.9512392 C15.2551997,7.74972838 14.8323003,7.74972838 14.4922311,7.9512392 Z" id="形状结合"></path>
            </g>
        </g>
    </g>
</svg>