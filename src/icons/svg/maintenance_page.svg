<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="21px" viewBox="0 0 20 21" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#8FC9FF" offset="0%"></stop>
            <stop stop-color="#1385F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#CDECFF" offset="0%"></stop>
            <stop stop-color="#CDECFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="后台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-12.000000, -313.000000)">
            <g id="nav/navSide" transform="translate(0.000000, 60.500000)">
                <g id="nav/navSide/item-parent备份-3" transform="translate(0.000000, 243.000000)">
                    <g id="页面维护" transform="translate(12.000000, 10.000000)">
                        <rect id="矩形" fill="#CDECFF" fill-rule="nonzero" opacity="0" x="0" y="0" width="20" height="20"></rect>
                        <path d="M18.75,5.91666667 L1.25,5.91666667 L1.25,2.02777778 C1.25,1.6 1.578125,1.25 1.97916667,1.25 L18.0208333,1.25 C18.421875,1.25 18.75,1.6 18.75,2.02777778 L18.75,5.91666667 Z M5.91666667,18.75 L2.02777778,18.75 C1.6,18.75 1.25,18.421875 1.25,18.0208333 L1.25,7.08333333 L5.91666667,7.08333333 L5.91666667,18.75 Z M18.0208333,18.75 L7.08333333,18.75 L7.08333333,7.08333333 L18.75,7.08333333 L18.75,18.0208333 C18.75,18.421875 18.421875,18.75 18.0208333,18.75 Z" id="形状结合" fill="url(#linearGradient-2)"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>