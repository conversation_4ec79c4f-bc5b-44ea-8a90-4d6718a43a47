<template>
  <div style="border: 1px solid #ccc; margin-top: 10px;">
    <!-- 工具栏 -->
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :default-config="toolbarConfig" />
    <!-- 编辑器 -->
    <Editor
      v-model="content"
      style="overflow-y: hidden;"
      :style="{height: height}"
      :default-config="editorConfig"
      @onCreated="onCreated"
      @onChange="handleContentChange"
    />
  </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { getToken } from '@/utils/auth'
import { removeFile } from '@/api/upload'
export default {
  name: 'MyQlEditor',
  components: {
    Editor, Toolbar
  },
  props: {
    height: {
      type: String,
      default: ''
    },
    uploadData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      content: '',
      contentImgs: [],
      oldImages: [],
      editor: null,
      toolbarConfig: {
        // 'uploadVideo'
        toolbarKeys: ['bold', 'underline', 'italic', 'through', 'sub', 'sup', 'clearStyle', 'color', 'bgColor', 'fontSize', 'fontFamily', 'indent', 'delIndent', 'justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify', 'lineHeight', 'deleteImage', 'editImage', 'viewImageLink', 'divider', 'emotion', 'insertLink', 'editLink', 'unLink', 'headerSelect', 'bulletedList', 'numberedList', 'uploadImage']
        // excludeKeys: [ "insertImage" ,"fullScreen", "insertVideo" ],
      },
      imageList1: [],
      videoList1: [],
      editorConfig: {
        placeholder: '请输入内容...',
        // autoFocus: false,

        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
          uploadImage: this.uploadImage(),
          insertImage: this.insertImage()
        }
        // insertVideo: this.insertVideo(),
        //   uploadVideo: this.uploadVideo()
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁 editor ，重要！！！
  },
  methods: {
    uploadImage() {
      const that = this
      console.log('that.uploadAction', that.uploadAction)
      return {
        server: process.env.VUE_APP_BASE_API + '/upload',
        fieldName: 'file',
        allowedFileTypes: [],
        maxFileSize: 10 * 1024 * 1024, //
        headers: { Authorization: getToken() },
        meta: that.uploadData,
        customInsert(res, insertFn) { // JS 语法
          console.log('insertFn', res)
          insertFn(res.data.url, res.data.id, res.data.url)
        }
      }
    },
    insertImage() {
      const that = this
      return {
        onInsertedImage(imageNode) { // JS 语法
          if (imageNode == null) return
          const { src, alt } = imageNode
          that.contentImgs.push({ url: src, id: alt })
          console.log('inserted image', src, alt)
        }
      }
    },
    uploadVideo() {
      const that = this
      return {
        server: process.env.VUE_APP_BASE_API + '/upload',
        fieldName: 'file',
        allowedFileTypes: [],
        headers: { Authorization: getToken() },
        meta: that.uploadData,
        customInsert(res, insertFn) { // JS 语法
          console.log('insertFn', res)
          insertFn(res.data.url, res.data.url + '?x-oss-process=video/snapshot,t_1000,w_0,h_0,ar_auto')
        }
      }
    },
    insertVideo() {
      const that = this
      return {
        onInsertedVideo(videoNode) {
          console.log('eeqweqwe') // JS 语法
          if (videoNode == null) return

          const { src } = videoNode
          that.videoList1.push({ url: src })
          console.log('inserted video', src)
        }
      }
    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 【注意】一定要用 Object.seal() 否则会报错
    },
    handleContentChange(editor) {
      // 检测删除的图片
      const newImages = this.extractImageUrls(editor.getHtml())
      const deletedImages = this.getDeletedImages(newImages, this.oldImages)
      console.log('Deleted Images:', deletedImages)
      this.oldImages = newImages
      if (deletedImages.length > 0) {
        const imgUrl = deletedImages[0]
        let allIndex = 0
        this.contentImgs.forEach((item, index) => {
          if (imgUrl === item.url) {
            allIndex = index
          }
        })
        const id = this.contentImgs[allIndex].id
        removeFile({ id }).then(res => {
          this.contentImgs.splice(allIndex, 1)
        })
      }
    },
    extractImageUrls(html) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')
      const images = Array.from(doc.querySelectorAll('img'))
      return images.map((image) => image.src)
    },
    getDeletedImages(newImages, oldImages) {
      return oldImages.filter((oldImage) => !newImages.includes(oldImage))
    }
  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

