<template>
  <div v-if="$route.name === 'ZoneManagerDetail'">
    <div class="page-top-nav">
      <div class="nav-left">
        <div class="circle" />
        <div class="title">{{ name }}</div>
      </div>
      <el-button type="primary" class="search-btn" @click="add()">添加</el-button>
    </div>
    <div class="app-container">
      <div class="page-top-container">
        <div class="search-div">
          <span class="search-tip">用户信息</span>
          <el-input
            v-model="temp.keyWord"
            class="phone-num"
            placeholder="手机号/用户名"
            clearable
          />
        </div>
        <div class="search-div">
          <span class="search-tip">年龄</span>
          <div class="select-type age-input-view">
            <el-input
              v-model="temp.ageStart"
              class="age-input"
              placeholder="输入"
            />
            /
            <el-input
              v-model="temp.ageEnd"
              class="age-input"
              placeholder="输入"
            />
          </div>
        </div>
        <div class="search-div">
          <span class="search-tip">资产</span>
          <el-select v-model="temp.property" placeholder="请选择" clearable class="select-type">
            <el-option
              v-for="item in ASSETSData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-div">
          <span class="search-tip">年收入</span>
          <el-select v-model="temp.yearIncome" placeholder="请选择" clearable class="select-type">
            <el-option
              v-for="item in INCOMEData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-div">
          <span class="search-tip">注册来源</span>
          <el-select v-model="temp.registerFrom" placeholder="请选择" clearable class="register-date">
            <el-option
              v-for="item in registerFromType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" class="search-btn" @click="search()">搜索</el-button>
      </div>
      <div class="page-top-container">
        <div class="search-div">
          <span class="search-tip">会员状态</span>
          <el-select v-model="temp.member" placeholder="请选择" clearable class="phone-num">
            <el-option
              v-for="item in MEMBER_LEVELData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-div">
          <span class="search-tip">职业</span>
          <el-select v-model="temp.profession" placeholder="请选择" clearable class="select-type">
            <el-option
              v-for="item in PROFESSIONData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-div">
          <span class="search-tip">学历</span>
          <el-select v-model="temp.educationalQualification" placeholder="请选择" clearable class="select-type">
            <el-option
              v-for="item in EDUCATEData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-div">
          <span class="search-tip">常住地</span>
          <el-cascader
            v-model="temp.residence"
            class="select-type"
            :props="{ value: 'fullCode', label: 'name' }"
            :options="cityData"
            placeholder="请选择"
            clearable
            style="width: 200px;"
          />
        </div>
        <div class="search-div">
          <span class="search-tip">注册日期</span>
          <el-date-picker
            v-model="registerDateValue"
            class="register-date"
            type="daterange"
            value-format="yyyy-MM-ddTHH:mm:ss"
            range-separator="/"
            start-placeholder="注册日期"
            end-placeholder="注册日期"
          />
        </div>
        <el-button class="search-btn" @click="clear()">清空</el-button>
      </div>
      <el-table
        ref="multipleTable"
        v-loading="listLoading"
        tooltip-effect="dark"
        style="width: 100%"
        :data="list"
        element-loading-text="Loading"
        border
        stripe
        fit
        highlight-current-row
        :header-cell-style="rowClass"
        :row-style="{height: '40px'}"
        height="calc(100vh - 290px - 48px - 34px)"
      >
        <el-table-column align="center" label="序号" width="52" fixed>
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户昵称" width="120" prop="userName" />
        <el-table-column align="center" label="手机号" width="120" prop="mobilePhone" />
        <el-table-column align="center" label="性别" width="80" prop="gender">
          <template slot-scope="scope">
            <span>{{ getStatusText(scope.row.gender, genderType) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="年龄" width="80" prop="age" />
        <el-table-column align="center" label="年收入" width="120" prop="yearIncome">
          <template slot-scope="scope">
            <span>{{ getStatusText(scope.row.yearIncome, INCOMEData) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="资产" width="120" prop="property">
          <template slot-scope="scope">
            <span>{{ getStatusText(scope.row.property, ASSETSData) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="职业" width="120" prop="profession">
          <template slot-scope="scope">
            <span>{{ getStatusText(scope.row.profession, PROFESSIONData) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="常住地" width="120" prop="residence">
          <template slot-scope="scope">
            <span>{{ scope.row.residenceCity ? scope.row.residenceCity.namePath.slice(1) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="会员状态" prop="member">
          <template slot-scope="scope">
            <span>{{ getStatusText(scope.row.member, MEMBER_LEVELData) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="140">
          <template slot-scope="scope">
            <div class="ope">
              <div class="edit ope" @click="gotoDetail(scope.row)">查看</div>
              <div class="del ope" @click="removeIt(scope.row.id)">移除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="paginationClass"
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="pageIndex"
        :page-size="pageSize"
        @current-change="currentChangeHandle"
      />
    </div>
  </div>
  <router-view v-else />
</template>

<script>
import { getPrefectureUserPageList, removePrefectureUser } from '@/api/prefecture'
import { registerFromType, userStatusType, genderType } from '@/config/enum-config.js'
import { getCityData } from '@/api/dictionary'
export default {
  name: 'ZoneManagerDetail',
  data() {
    return {
      list: [],
      listLoading: true,
      pageSize: 10,
      pageIndex: 1,
      total: 0,
      registerFromType: registerFromType,
      userStatusType: userStatusType,
      genderType: genderType,
      registerDateValue: '',
      temp: {
        keyWord: '',
        ageStart: '',
        ageEnd: '',
        property: '',
        yearIncome: '',
        registerFrom: '',
        member: '',
        profession: '',
        educationalQualification: '',
        residence: '',
        startTime: '',
        endTime: ''
      },
      prefectureId: '',
      gender: '',
      name: '',
      cityData: []
    }
  },
  async created() {
    this.prefectureId = this.$route.query.prefectureId
    this.gender = this.$route.query.gender
    this.name = this.$route.query.name
    const cRes = await getCityData({})
    this.cityData = cRes.data
    this.fetchData()
    this.getDicListByTypes()
  },
  methods: {
    add() {
      this.$router.push({ path: '/zoneManagerAdd', query: { gender: this.gender, name: this.name, prefectureId: this.prefectureId }})
    },
    gotoDetail(row) {
      this.$router.push({ path: '/userDetail', query: { id: row.id }})
    },
    fetchData() {
      this.listLoading = true
      const params = JSON.parse(JSON.stringify(this.temp))
      params['pageIndex'] = this.pageIndex
      params['pageSize'] = this.pageSize
      params['prefectureId'] = this.prefectureId
      getPrefectureUserPageList(params).then(response => {
        this.list = response.data
        this.listLoading = false
        this.total = response.total
      })
    },
    search() {
      this.pageIndex = 1
      if (this.registerDateValue) {
        this.temp.startTime = this.registerDateValue[0]
        this.temp.endTime = this.registerDateValue[1]
      } else {
        this.temp.startTime = ''
        this.temp.endTime = ''
      }
      if (this.temp.residence && this.temp.residence.length > 0) {
        this.temp.residence = this.temp.residence[this.temp.residence.length - 1]
      }
      this.fetchData()
    },
    clear() {
      this.temp = {}
      this.registerDateValue = ''
    },
    removeIt(id) {
      this.$confirmTwice('确定要移除该用户吗？').then((index) => {
        if (index === 1) {
          removePrefectureUser({ idList: [id] }).then(res => {
            this.fetchData()
          })
        }
      })
    },
    currentChangeHandle(val) {
      this.pageIndex = val
      this.fetchData()
    },
    // 表格头第一行上色
    rowClass({ row, rowIndex }) {
       // 表头行标号为0
      return { background: '#E4F4FF', color: '#666666', fontSize: '14px !impotent', fontFamily: 'PingFangSC-Semibold, PingFang SC', fontWeight: '600', height: '36px' }
    }

  }
}
</script>
<style lang="scss">
.age-input {
  .el-input__inner {
    border-width: 0px !important;
  }
}
</style>
<style lang="scss" scoped>
.page-top-container {
  .search-div {
    display: flex;
    align-items: center;
    margin-right: 12px;
    .search-tip {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      margin-right: 8px;
    }
    .phone-num {
      width: 124px;
      background: #FFFFFF;
      border-radius: 4px;
    }
    .select-type {
      width: 132px;
      background: #FFFFFF;
      border-radius: 4px;
    }
    .age-input-view {
      display: flex;
      flex-direction: row;
      align-items: center;
      border: 1px solid #DCDFE6;
    }
    .age-input {
      flex: 1;
    }
    .select-type1 {
      width: 98px;
      background: #FFFFFF;
      border-radius: 4px;
    }
    .search-btn {
      width: 72px;
      border-radius: 4px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      margin-right: 12px;
  }
  .ope-top-btn {
      // width: 72px;
      border-radius: 4px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      margin-right: 12px;
  }
  .register-date {
    width: 244px;
    background: #FFFFFF;
    border-radius: 4px;
  }
  }
  // height: 56px;
  // background: #F0F0F0;
  // border: 1px solid #CACBCD;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  // padding: 0 12px;
  .register-from {
    width: 176px;
    background: #FFFFFF;
    border-radius: 4px;
    margin-left: 12px;
  }
  .login-date {
    width: 260px;
    background: #FFFFFF;
    border-radius: 4px;
    margin-left: 12px;
  }
}
.status {
  height: 24px;
  width: 58px;
  border-radius: 2px;
  line-height: 24px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  text-align: center;
  margin: 0 auto;
}
.status-EFFECTIVE {
  color: #4AC110;
  background: #E4FFD8;
}
.status-INVALID {
  color: #FF8600;
  background: #FFEEDB;
}
.status-DISABLED {
  color: #F12933;
  background: #FFEBEB;
}
.ope {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 24px;
  padding: 0 8px;
  border-radius: 4px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  .edit {
    background: #3C75BE;
  }
  .del {
    background: #F12933;
    margin-left: 5px;
  }
}

.ope-btn {
  height: 24px;
  width: 44px;
  background: #F12933;
  border-radius: 4px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
}
.paginationClass {
  position: fixed;
  bottom: 16px;
  right: 16px;
  // text-align: right;
}
.ope-dropdown {
  width: 32px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page-top-nav {
  width: 100%;
  height: 48px;
  background: #D9EEFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 14px;
  border-bottom: 2px solid #0086F5;
  .nav-left {
    display: flex;
    align-items: center;
  }
  .circle {
    width: 10px;
    height: 10px;
    background: #3DA0FC;
    border-radius: 50%;
  }
  .title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #222222;
    margin-left: 6px;
  }
}
</style>
